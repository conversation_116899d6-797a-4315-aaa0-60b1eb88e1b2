<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use Carbon\Carbon;
use App\Traits\SupplierHelper;

class SupplierReturnController extends Controller
{
    use SupplierHelper;

    /**
     * Display a listing of returns for the current supplier.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        $query = ReturnModel::with(['product', 'supplierDelivery'])
            ->where('supplier_id', $supplier->id)
            ->whereNotNull('supplier_delivery_id'); // Only warehouse-to-supplier returns

        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);
        
        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();
        
        // Apply date filter
        $query->whereBetween('return_date', [$startDate, $endDate]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Sort by return date (newest first)
        $query->orderBy('return_date', 'desc');

        $returns = $query->paginate(15);

        // Get statistics for the current month
        $stats = [
            'total' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereNotNull('supplier_delivery_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->count(),
            'requested' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereNotNull('supplier_delivery_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'requested')
                ->count(),
            'approved' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereNotNull('supplier_delivery_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'approved')
                ->count(),
            'rejected' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereNotNull('supplier_delivery_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'rejected')
                ->count(),
        ];

        return view('supplier.returns.index', compact('returns', 'stats', 'filterMonth'));
    }

    /**
     * Display the specified return.
     */
    public function show(ReturnModel $return)
    {
        // Ensure this return belongs to the current supplier
        $supplier = $this->getCurrentSupplier();
        
        if ($return->supplier_id !== $supplier->id) {
            abort(403, 'Unauthorized access to return record');
        }

        $return->load(['product', 'supplierDelivery']);

        return view('supplier.returns.show', compact('return'));
    }

    /**
     * Approve a return request.
     */
    public function approve(Request $request, ReturnModel $return)
    {
        // Ensure this return belongs to the current supplier
        $supplier = $this->getCurrentSupplier();
        
        if ($return->supplier_id !== $supplier->id) {
            abort(403, 'Unauthorized access to return record');
        }

        // Only allow approval of requested returns
        if ($return->status !== 'requested') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Hanya retur dengan status "Diminta" yang dapat disetujui');
        }

        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $return->update([
            'status' => 'approved',
            'approved_date' => now(),
            'admin_notes' => $validatedData['admin_notes'] ?? null,
        ]);

        return redirect()->route('supplier.returns.index')
            ->with('success', 'Retur berhasil disetujui');
    }

    /**
     * Reject a return request.
     */
    public function reject(Request $request, ReturnModel $return)
    {
        // Ensure this return belongs to the current supplier
        $supplier = $this->getCurrentSupplier();
        
        if ($return->supplier_id !== $supplier->id) {
            abort(403, 'Unauthorized access to return record');
        }

        // Only allow rejection of requested returns
        if ($return->status !== 'requested') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Hanya retur dengan status "Diminta" yang dapat ditolak');
        }

        $validatedData = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'Alasan penolakan wajib diisi',
            'admin_notes.string' => 'Alasan penolakan harus berupa teks',
            'admin_notes.max' => 'Alasan penolakan maksimal 1000 karakter',
        ]);

        $return->update([
            'status' => 'rejected',
            'approved_date' => now(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);

        return redirect()->route('supplier.returns.index')
            ->with('success', 'Retur berhasil ditolak');
    }
}
