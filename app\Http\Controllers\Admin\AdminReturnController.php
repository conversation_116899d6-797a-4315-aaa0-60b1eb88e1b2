<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\TimePeriodFilter;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Supplier;
use Carbon\Carbon;

class AdminReturnController extends Controller
{
    use TimePeriodFilter;

    /**
     * Display a listing of returns for warehouse admin.
     */
    public function index(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy'])
            ->whereNull('store_id') // Only warehouse returns (store_id is null)
            ->whereNotNull('supplier_id'); // Only returns to suppliers

        // Apply time period filter
        $this->applyTimePeriodFilter($query, $request, 'return_date');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', "%{$search}%");
                })->orWhereHas('supplier', function ($supplierQuery) use ($search) {
                    $supplierQuery->where('name', 'like', "%{$search}%");
                })->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply reason filter
        if ($request->filled('reason')) {
            $query->where('reason', $request->get('reason'));
        }

        // Sort by newest first
        $returns = $query->orderBy('return_date', 'desc')->paginate(15);

        // Get statistics for current period
        $statsQuery = ReturnModel::whereNull('store_id')->whereNotNull('supplier_id');
        $this->applyTimePeriodFilter($statsQuery, $request, 'return_date');

        $stats = [
            'total' => $statsQuery->count(),
            'requested' => $statsQuery->where('status', 'requested')->count(),
            'approved' => $statsQuery->where('status', 'approved')->count(),
            'completed' => $statsQuery->where('status', 'completed')->count(),
        ];

        return view('admin.returns.index', compact('returns', 'stats'));
    }

    /**
     * Store a return based on supplier delivery shortage.
     */
    public function storeFromSupplierDelivery(Request $request)
    {
        $validatedData = $request->validate([
            'supplier_delivery_id' => 'required|exists:supplier_deliveries,id',
            'product_id' => 'required|exists:products,id',
            'supplier_id' => 'required|exists:suppliers,id',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|in:damaged,expired,defective,overstock,shortage,other',
            'description' => 'required|string|max:1000',
            'return_date' => 'required|date|before_or_equal:today',
        ], [
            'supplier_delivery_id.required' => 'Pengiriman supplier wajib dipilih',
            'supplier_delivery_id.exists' => 'Pengiriman supplier tidak valid',
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'supplier_id.required' => 'Supplier wajib dipilih',
            'supplier_id.exists' => 'Supplier tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'reason.required' => 'Alasan wajib dipilih',
            'reason.in' => 'Alasan tidak valid',
            'description.required' => 'Deskripsi wajib diisi',
            'description.max' => 'Deskripsi maksimal 1000 karakter',
            'return_date.required' => 'Tanggal retur wajib diisi',
            'return_date.date' => 'Format tanggal tidak valid',
            'return_date.before_or_equal' => 'Tanggal retur tidak boleh lebih dari hari ini',
        ]);

        // Validate that the supplier delivery belongs to the specified supplier
        $supplierDelivery = \App\Models\SupplierDelivery::findOrFail($validatedData['supplier_delivery_id']);
        if ($supplierDelivery->supplier_id !== $validatedData['supplier_id']) {
            return redirect()->back()
                ->with('error', 'Pengiriman supplier tidak sesuai dengan supplier yang dipilih')
                ->withInput();
        }

        // Calculate max returnable quantity using smart return logic
        $approvedReturns = ReturnModel::where('supplier_delivery_id', $supplierDelivery->id)
            ->where('status', 'approved')
            ->sum('quantity');

        $totalAccountedFor = ($supplierDelivery->received_quantity ?? 0) + $approvedReturns;
        $maxReturnable = max(0, $supplierDelivery->quantity - $totalAccountedFor);

        if ($validatedData['quantity'] > $maxReturnable) {
            return redirect()->back()
                ->with('error', "Jumlah retur melebihi batas maksimal yang dapat diretur ({$maxReturnable} unit)")
                ->withInput();
        }

        $validatedData['store_id'] = null; // Warehouse return
        $validatedData['requested_by'] = auth()->id();
        $validatedData['status'] = 'requested';

        $return = ReturnModel::create($validatedData);

        return redirect()->route('admin.supplier-deliveries.index')
            ->with('success', 'Permintaan retur berhasil dibuat dan menunggu persetujuan supplier');
    }

    /**
     * Approve a return request.
     */
    public function approve(Request $request, ReturnModel $return)
    {
        if ($return->status !== 'requested') {
            return redirect()->route('admin.returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }

        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        $return->update([
            'status' => 'approved',
            'approved_date' => now(),
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);

        return redirect()->route('admin.returns.index')
            ->with('success', 'Permintaan retur berhasil disetujui');
    }

    /**
     * Reject a return request.
     */
    public function reject(Request $request, ReturnModel $return)
    {
        if ($return->status !== 'requested') {
            return redirect()->route('admin.returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }

        $validatedData = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'Alasan penolakan wajib diisi',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        $return->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);

        return redirect()->route('admin.returns.index')
            ->with('success', 'Permintaan retur berhasil ditolak');
    }

    /**
     * Display the specified return.
     */
    public function show(ReturnModel $return)
    {
        // Only allow viewing warehouse returns to suppliers
        if (!is_null($return->store_id) || is_null($return->supplier_id)) {
            abort(403, 'Akses ditolak');
        }

        $return->load(['product', 'supplier', 'supplierDelivery', 'requestedBy', 'approvedBy']);

        return view('admin.returns.show', compact('return'));
    }
}
