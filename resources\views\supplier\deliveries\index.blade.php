@extends('layouts.supplier')

@section('title', '<PERSON><PERSON><PERSON> - Dashboard Supplier')
@section('page-title', '<PERSON><PERSON><PERSON> Pengiriman')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Ke<PERSON><PERSON>giriman</h1>
                    <p class="text-gray-600 mt-1">Pantau dan kelola pengiriman produk ke gudang</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('supplier.deliveries.create') }}" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Pengiriman
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['total']) }}</div>
            <div class="supplier-dashboard-stat-label">Total Pengiriman</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['pending']) }}</div>
            <div class="supplier-dashboard-stat-label">Pending</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['received']) }}</div>
            <div class="supplier-dashboard-stat-label">Diterima</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['partial']) }}</div>
            <div class="supplier-dashboard-stat-label">Sebagian</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="supplier-dashboard-label">Periode</label>
                    <input type="month"
                           name="month"
                           value="{{ $filterMonth }}"
                           class="supplier-dashboard-input">
                </div>

                <!-- Search -->
                <div>
                    <label class="supplier-dashboard-label">Cari</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Cari supplier atau produk..."
                           class="supplier-dashboard-input">
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="supplier-dashboard-label">Status</label>
                    <select name="status" class="supplier-dashboard-select">
                        <option value="">Semua Status</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="received" {{ request('status') === 'received' ? 'selected' : '' }}>Diterima</option>
                        <option value="partial" {{ request('status') === 'partial' ? 'selected' : '' }}>Sebagian</option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Deliveries Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Daftar Pengiriman</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Supplier</th>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Kirim</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($deliveries as $delivery)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->supplier->name }}</div>
                                <div class="text-sm text-gray-500">{{ $delivery->supplier->contact_person }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->product->name }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($delivery->quantity) }}</div>
                                @if($delivery->received_quantity)
                                <div class="text-sm text-gray-500">Diterima: {{ number_format($delivery->received_quantity) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ auth()->user()->formatDate($delivery->delivery_date) }}</div>
                                @if($delivery->received_date)
                                <div class="text-sm text-gray-500">Diterima: {{ auth()->user()->formatDate($delivery->received_date) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                    @if($delivery->status === 'pending') bg-yellow-100 text-yellow-800
                                    @elseif($delivery->status === 'received') bg-green-100 text-green-800
                                    @elseif($delivery->status === 'partial') bg-blue-100 text-blue-800
                                    @elseif($delivery->status === 'cancelled') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    @if($delivery->status === 'pending') Pending
                                    @elseif($delivery->status === 'received') Diterima
                                    @elseif($delivery->status === 'partial') Sebagian
                                    @elseif($delivery->status === 'cancelled') Dibatalkan
                                    @else {{ ucfirst($delivery->status) }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('supplier.deliveries.show', $delivery) }}"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    @if($delivery->status === 'pending')
                                    <button onclick="openDeleteModal('{{ $delivery->id }}', '{{ $delivery->product->name }}')"
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Hapus
                                    </button>
                                    @endif
                                    {{-- Smart Return Button - Only show for received/partial deliveries with returnable quantity --}}
                                    @if(in_array($delivery->status, ['received', 'partial']) && $delivery->max_returnable_quantity > 0)
                                    <button onclick="openSmartReturnModal('{{ $delivery->id }}', '{{ $delivery->product->name }}', {{ $delivery->max_returnable_quantity }})"
                                            class="text-orange-600 hover:text-orange-800 text-sm font-medium">
                                        Smart Retur
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada pengiriman</p>
                                    <p class="mb-4">Mulai dengan menambahkan pengiriman pertama Anda</p>
                                    <a href="{{ route('supplier.deliveries.create') }}" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                                        Tambah Pengiriman
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($deliveries->hasPages())
            <div class="mt-6">
                {{ $deliveries->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
<!-- Deletion Confirmation Modal -->
<div id="deleteModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Konfirmasi Hapus Pengiriman</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="supplier-dashboard-modal-body">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Hapus Pengiriman</h4>
                    <p class="text-sm text-gray-600 mb-4">
                        Apakah Anda yakin ingin menghapus pengiriman produk <strong id="deleteProductName"></strong>?
                    </p>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-yellow-800">
                                <p class="font-medium">Perhatian:</p>
                                <p>Pengiriman yang sudah diterima oleh admin gudang tidak dapat dihapus. Hanya pengiriman dengan status "Menunggu" yang dapat dihapus.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="supplier-dashboard-modal-footer">
            <button type="button" onclick="closeDeleteModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                Batal
            </button>
            <form id="deleteForm" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Ya, Hapus Pengiriman
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Smart Return Modal -->
<div id="smartReturnModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Smart Retur Produk</h3>
            <button type="button" onclick="closeSmartReturnModal()" class="supplier-dashboard-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="smartReturnForm" method="POST" action="{{ route('admin.supplier-deliveries.store-return') }}">
            @csrf
            <input type="hidden" name="supplier_delivery_id" id="returnDeliveryId">
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-blue-800">
                                <p class="font-medium mb-1">Smart Retur</p>
                                <p>Sistem akan otomatis menghitung jumlah maksimal yang dapat diretur berdasarkan selisih antara jumlah yang dikirim dan yang diterima.</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Produk</label>
                        <div class="bg-gray-50 border border-gray-300 rounded-md px-3 py-2">
                            <span id="returnProductName" class="text-gray-900 font-medium"></span>
                        </div>
                    </div>

                    <div>
                        <label for="returnQuantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah Retur <span class="text-red-500">*</span>
                        </label>
                        <input type="number"
                               name="quantity"
                               id="returnQuantity"
                               min="1"
                               required
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Masukkan jumlah yang akan diretur">
                        <p class="text-sm text-gray-500 mt-1">
                            Maksimal: <span id="maxReturnableQuantity" class="font-medium text-blue-600"></span> unit
                        </p>
                    </div>

                    <div>
                        <label for="returnReason" class="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Retur <span class="text-red-500">*</span>
                        </label>
                        <select name="reason" id="returnReason" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Pilih alasan retur</option>
                            <option value="damaged">Produk Rusak</option>
                            <option value="defective">Produk Cacat</option>
                            <option value="wrong_item">Barang Salah</option>
                            <option value="excess_quantity">Kelebihan Jumlah</option>
                            <option value="quality_issue">Masalah Kualitas</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div>
                        <label for="returnDescription" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi (Opsional)
                        </label>
                        <textarea name="description"
                                  id="returnDescription"
                                  rows="3"
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Jelaskan detail alasan retur jika diperlukan..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeSmartReturnModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-warning">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                    </svg>
                    Buat Retur
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Smart Return Modal Functions
function openSmartReturnModal(deliveryId, productName, maxReturnable) {
    const modal = document.getElementById('smartReturnModal');
    const deliveryIdInput = document.getElementById('returnDeliveryId');
    const productNameElement = document.getElementById('returnProductName');
    const quantityInput = document.getElementById('returnQuantity');
    const maxQuantityElement = document.getElementById('maxReturnableQuantity');

    deliveryIdInput.value = deliveryId;
    productNameElement.textContent = productName;
    quantityInput.max = maxReturnable;
    maxQuantityElement.textContent = maxReturnable;

    modal.classList.add('active');
}

function closeSmartReturnModal() {
    const modal = document.getElementById('smartReturnModal');
    modal.classList.remove('active');

    // Reset form
    document.getElementById('smartReturnForm').reset();
}

function openDeleteModal(deliveryId, productName) {
    const modal = document.getElementById('deleteModal');
    const form = document.getElementById('deleteForm');
    const productNameElement = document.getElementById('deleteProductName');

    form.action = `/supplier/deliveries/${deliveryId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('active');
}

// Close modals when clicking outside
document.getElementById('smartReturnModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSmartReturnModal();
    }
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeSmartReturnModal();
        closeDeleteModal();
    }
});
</script>
@endpush

@endsection
