@extends('layouts.supplier')

@section('title', '<PERSON><PERSON><PERSON> - Dashboard Supplier')
@section('page-title', 'Kelola Retur')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Ke<PERSON><PERSON></h1>
                    <p class="text-gray-600 mt-1">Kelola permintaan retur dari gudang</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Lihat Pengiriman
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <!-- Total Returns -->
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['total']) }}</div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>

        <!-- Requested Returns -->
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['requested']) }}</div>
            <div class="supplier-dashboard-stat-label">Menunggu Persetujuan</div>
        </div>

        <!-- Approved Returns -->
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['approved']) }}</div>
            <div class="supplier-dashboard-stat-label">Disetujui</div>
        </div>

        <!-- Rejected Returns -->
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['rejected']) }}</div>
            <div class="supplier-dashboard-stat-label">Ditolak</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="flex flex-col sm:flex-row gap-4">
                <!-- Month Filter -->
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Filter Bulan</label>
                    <input type="month" 
                           name="month" 
                           value="{{ $filterMonth }}" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Search -->
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Cari Produk</label>
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}" 
                           placeholder="Nama produk..." 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Status Filter -->
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Semua Status</option>
                        <option value="requested" {{ request('status') === 'requested' ? 'selected' : '' }}>Diminta</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Daftar Retur</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal Retur</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                @if($return->description)
                                <div class="text-sm text-gray-500">{{ Str::limit($return->description, 50) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-gray-900">{{ number_format($return->quantity) }}</span>
                                <span class="text-sm text-gray-500">unit</span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="text-sm text-gray-600">
                                    @if($return->reason === 'damaged') Produk Rusak
                                    @elseif($return->reason === 'defective') Produk Cacat
                                    @elseif($return->reason === 'wrong_item') Barang Salah
                                    @elseif($return->reason === 'excess_quantity') Kelebihan Jumlah
                                    @elseif($return->reason === 'quality_issue') Masalah Kualitas
                                    @elseif($return->reason === 'other') Lainnya
                                    @else {{ ucfirst($return->reason) }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="text-sm text-gray-900">{{ auth()->user()->formatDate($return->return_date) }}</span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="supplier-dashboard-status-badge supplier-dashboard-status-{{ $return->status }}">
                                    @if($return->status === 'requested') Diminta
                                    @elseif($return->status === 'approved') Disetujui
                                    @elseif($return->status === 'rejected') Ditolak
                                    @else {{ ucfirst($return->status) }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('supplier.returns.show', $return) }}"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    @if($return->status === 'requested')
                                    <button onclick="approveReturn('{{ $return->id }}')"
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Setujui
                                    </button>
                                    <button onclick="rejectReturn('{{ $return->id }}')"
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Tolak
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada retur</p>
                                    <p class="mb-4">Belum ada permintaan retur pada periode ini</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($returns->hasPages())
            <div class="mt-6">
                {{ $returns->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Approve Return Modal -->
<div id="approveModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Setujui Retur</h3>
            <button type="button" onclick="closeApproveModal()" class="supplier-dashboard-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveForm" method="POST">
            @csrf
            @method('PATCH')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Setujui Permintaan Retur</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyetujui permintaan retur ini?
                        </p>
                        <div>
                            <label for="approveNotes" class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan (Opsional)
                            </label>
                            <textarea name="admin_notes"
                                      id="approveNotes"
                                      rows="3"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeApproveModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Ya, Setujui
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reject Return Modal -->
<div id="rejectModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Tolak Retur</h3>
            <button type="button" onclick="closeRejectModal()" class="supplier-dashboard-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="rejectForm" method="POST">
            @csrf
            @method('PATCH')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Tolak Permintaan Retur</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Berikan alasan penolakan untuk permintaan retur ini.
                        </p>
                        <div>
                            <label for="rejectNotes" class="block text-sm font-medium text-gray-700 mb-2">
                                Alasan Penolakan <span class="text-red-500">*</span>
                            </label>
                            <textarea name="admin_notes"
                                      id="rejectNotes"
                                      rows="4"
                                      required
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
                                      placeholder="Jelaskan alasan penolakan retur..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeRejectModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Ya, Tolak
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function approveReturn(returnId) {
    const modal = document.getElementById('approveModal');
    const form = document.getElementById('approveForm');

    form.action = `/supplier/returns/${returnId}/approve`;
    modal.classList.add('active');
}

function closeApproveModal() {
    const modal = document.getElementById('approveModal');
    modal.classList.remove('active');

    // Reset form
    document.getElementById('approveForm').reset();
}

function rejectReturn(returnId) {
    const modal = document.getElementById('rejectModal');
    const form = document.getElementById('rejectForm');

    form.action = `/supplier/returns/${returnId}/reject`;
    modal.classList.add('active');
}

function closeRejectModal() {
    const modal = document.getElementById('rejectModal');
    modal.classList.remove('active');

    // Reset form
    document.getElementById('rejectForm').reset();
}

// Close modals when clicking outside
document.getElementById('approveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApproveModal();
    }
});

document.getElementById('rejectModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRejectModal();
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeApproveModal();
        closeRejectModal();
    }
});
</script>
@endpush

@endsection
