<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Dashboard - Indah Berkah Abadi')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Analytics Sidebar Styles -->
    <link rel="stylesheet" href="{{ asset('css/analytics-sidebar.css') }}"?v={{ time() }}">

    <!-- Admin Dashboard Specific Styles -->
    <style>
        /* Admin Dashboard Specific CSS with prefixed classes */
        .admin-dashboard-container {
            min-height: 100vh;
            height: 100vh;
            background-color: #f8fafc;
            overflow: hidden;
        }

        /* Status Icons */
        .admin-dashboard-status-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .admin-dashboard-status-icon.admin-dashboard-status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }

        .admin-dashboard-status-icon.admin-dashboard-status-received {
            background-color: #d1fae5;
            color: #059669;
        }

        .admin-dashboard-status-icon.admin-dashboard-status-partial {
            background-color: #dbeafe;
            color: #2563eb;
        }

        .admin-dashboard-status-icon.admin-dashboard-status-cancelled {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .admin-dashboard-status-icon.admin-dashboard-status-default {
            background-color: #f3f4f6;
            color: #6b7280;
        }

        /* Modal System */
        .admin-dashboard-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }

        .admin-dashboard-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .admin-dashboard-modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.95);
            transition: transform 0.3s ease-in-out;
        }

        .admin-dashboard-modal.active .admin-dashboard-modal-content {
            transform: scale(1);
        }

        .admin-dashboard-modal-header {
            padding: 1.5rem 1.5rem 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }

        .admin-dashboard-modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .admin-dashboard-modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: #f3f4f6;
            border-radius: 50%;
            color: #6b7280;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease-in-out;
        }

        .admin-dashboard-modal-close:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .admin-dashboard-modal-body {
            padding: 0 1.5rem;
        }

        .admin-dashboard-modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
        }

        /* Form Elements */
        .admin-dashboard-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease-in-out;
        }

        .admin-dashboard-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .admin-dashboard-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            resize: vertical;
            transition: border-color 0.2s ease-in-out;
        }

        .admin-dashboard-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .admin-dashboard-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background-color: white;
            transition: border-color 0.2s ease-in-out;
        }

        .admin-dashboard-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Button Styles */
        .admin-dashboard-btn-warning {
            background: #f59e0b;
            color: white;
        }

        .admin-dashboard-btn-warning:hover {
            background: #d97706;
        }

        .admin-dashboard-sidebar {
            width: 280px;
            background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            min-height: 100vh;
            max-height: 100vh;
            overflow-y: auto;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 50;
            display: flex;
            flex-direction: column;
        }

        .admin-dashboard-sidebar.open {
            transform: translateX(0);
        }

        .admin-dashboard-main {
            margin-left: 0;
            transition: margin-left 0.3s ease;
            min-height: 100vh;
            background-color: #f8fafc;
            display: flex;
            flex-direction: column;
        }

        .admin-dashboard-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 40;
        }

        .admin-dashboard-content {
            padding: 1.5rem;
            flex: 1;
            background-color: #f8fafc;
            overflow-y: auto;
            min-height: 0;
        }

        .admin-dashboard-logo {
            padding: 1.5rem;
            border-bottom: 1px solid #475569;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .admin-dashboard-logo-icon {
            width: 40px;
            height: 40px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.125rem;
            color: white;
        }

        .admin-dashboard-nav {
            padding: 1.5rem 0;
            flex: 1;
            overflow-y: auto;
        }

        .admin-dashboard-nav-section {
            margin-bottom: 2rem;
        }

        .admin-dashboard-nav-title {
            padding: 0 1.5rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: #94a3b8;
        }

        .admin-dashboard-nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .admin-dashboard-nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .admin-dashboard-nav-link.active {
            background-color: rgba(59, 130, 246, 0.2);
            color: white;
            border-left-color: #3b82f6;
        }

        .admin-dashboard-nav-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .admin-dashboard-mobile-toggle {
            display: block;
            background: none;
            border: none;
            padding: 0.5rem;
            color: #64748b;
            cursor: pointer;
        }

        .admin-dashboard-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 40;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .admin-dashboard-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .admin-dashboard-user-menu {
            position: relative;
        }

        .admin-dashboard-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        }

        .admin-dashboard-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .admin-dashboard-dropdown-item {
            display: block;
            padding: 0.75rem 1rem;
            color: #374151;
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .admin-dashboard-dropdown-item:hover {
            background-color: #f3f4f6;
        }

        /* Responsive Design */
        @media (min-width: 1024px) {
            .admin-dashboard-container {
                display: flex;
            }

            .admin-dashboard-sidebar {
                position: static;
                transform: translateX(0);
                flex-shrink: 0;
                height: 100vh;
                min-height: 100vh;
                max-height: 100vh;
            }

            .admin-dashboard-main {
                margin-left: 0;
                flex: 1;
                min-width: 0;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
            }

            .admin-dashboard-mobile-toggle {
                display: none;
            }

            /* Desktop Pagination */
            .admin-dashboard-pagination {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .admin-dashboard-pagination-info {
                justify-content: flex-start;
                width: auto;
            }

            .admin-dashboard-pagination-nav {
                justify-content: flex-end;
                gap: 0.5rem;
            }

            .admin-dashboard-pagination-text-desktop {
                display: inline;
            }

            .admin-dashboard-pagination-btn {
                padding: 0.625rem 1rem;
                min-height: 44px;
                min-width: auto;
            }
        }

        @media (max-width: 768px) {
            .admin-dashboard-container {
                height: auto;
                overflow: visible;
            }

            .admin-dashboard-main {
                min-height: 100vh;
            }

            .admin-dashboard-content {
                padding: 1rem;
            }

            .admin-dashboard-header {
                padding: 1rem;
            }

            /* Mobile Pagination */
            .admin-dashboard-pagination {
                flex-direction: column;
                gap: 1rem;
            }

            .admin-dashboard-pagination-nav {
                gap: 0.25rem;
            }

            .admin-dashboard-pagination-text {
                font-size: 0.8125rem;
                text-align: center;
            }
        }

        /* Extra small screens */
        @media (max-width: 480px) {
            .admin-dashboard-pagination-btn {
                padding: 0.375rem 0.5rem;
                font-size: 0.75rem;
                min-height: 36px;
                min-width: 36px;
            }

            .admin-dashboard-pagination-nav {
                gap: 0.125rem;
            }

            .admin-dashboard-pagination-text {
                font-size: 0.75rem;
                padding: 0 0.5rem;
            }

            .admin-dashboard-pagination-icon {
                width: 12px;
                height: 12px;
            }

            .admin-dashboard-pagination-dots {
                min-width: 36px;
                padding: 0.375rem 0.125rem;
            }
        }

        /* Card Styles */
        .admin-dashboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .admin-dashboard-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .admin-dashboard-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .admin-dashboard-card-content {
            padding: 1.5rem;
        }

        /* Button Styles */
        .admin-dashboard-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .admin-dashboard-btn-primary {
            background: #3b82f6;
            color: white;
        }

        .admin-dashboard-btn-primary:hover {
            background: #2563eb;
        }

        .admin-dashboard-btn-secondary {
            background: #6b7280;
            color: white;
        }

        .admin-dashboard-btn-secondary:hover {
            background: #4b5563;
        }

        .admin-dashboard-btn-danger {
            background: #dc2626;
            color: white;
        }

        .admin-dashboard-btn-danger:hover {
            background: #b91c1c;
        }

        /* Stats Card */
        .admin-dashboard-stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .admin-dashboard-stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0.5rem 0;
        }

        .admin-dashboard-stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .admin-dashboard-stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .admin-dashboard-stat-icon.blue {
            background: #dbeafe;
            color: #3b82f6;
        }

        .admin-dashboard-stat-icon.green {
            background: #dcfce7;
            color: #16a34a;
        }

        .admin-dashboard-stat-icon.purple {
            background: #f3e8ff;
            color: #9333ea;
        }

        .admin-dashboard-stat-icon.orange {
            background: #fed7aa;
            color: #ea580c;
        }

        /* Alert Styles */
        .admin-dashboard-alert {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .admin-dashboard-alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .admin-dashboard-alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .admin-dashboard-alert-warning {
            background: #fefce8;
            color: #a16207;
            border: 1px solid #fef3c7;
        }

        .admin-dashboard-alert-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #dbeafe;
        }

        /* Pagination Styles */
        .admin-dashboard-pagination {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            align-items: center;
        }

        .admin-dashboard-pagination-info {
            display: flex;
            justify-content: center;
            width: 100%;
        }

        .admin-dashboard-pagination-text {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .admin-dashboard-pagination-text-desktop {
            display: none;
        }

        .admin-dashboard-pagination-nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .admin-dashboard-pagination-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
            font-size: 0.875rem;
            min-height: 40px;
            min-width: 40px;
            justify-content: center;
            line-height: 1;
        }

        .admin-dashboard-pagination-btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .admin-dashboard-pagination-btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        .admin-dashboard-pagination-btn-secondary {
            background: white;
            color: #374151;
            border-color: #e2e8f0;
        }

        .admin-dashboard-pagination-btn-secondary:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }

        .admin-dashboard-pagination-btn-active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            font-weight: 600;
        }

        .admin-dashboard-pagination-btn-disabled {
            background: #f9fafb;
            color: #9ca3af;
            border-color: #e5e7eb;
            cursor: not-allowed;
        }

        .admin-dashboard-pagination-icon {
            width: 14px;
            height: 14px;
            flex-shrink: 0;
        }

        .admin-dashboard-pagination-dots {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 0.25rem;
            color: #9ca3af;
            font-weight: 500;
            font-size: 0.875rem;
            min-width: 40px;
        }

        /* Focus states for accessibility */
        .admin-dashboard-pagination-btn:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Hover animations */
        .admin-dashboard-pagination-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .admin-dashboard-pagination-btn-disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* Active state animation */
        .admin-dashboard-pagination-btn-active {
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        /* Form Styles */
        .admin-dashboard-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .admin-dashboard-input,
        .admin-dashboard-select,
        .admin-dashboard-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .admin-dashboard-input:focus,
        .admin-dashboard-select:focus,
        .admin-dashboard-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .admin-dashboard-input:invalid,
        .admin-dashboard-select:invalid,
        .admin-dashboard-textarea:invalid {
            border-color: #dc2626;
        }

        .admin-dashboard-error {
            font-size: 0.875rem;
            color: #dc2626;
            margin-top: 0.25rem;
        }

        .admin-dashboard-textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* Modal Styles */
        .admin-dashboard-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .admin-dashboard-modal.active {
            display: flex;
        }

        .admin-dashboard-modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            margin: 20px;
        }

        .admin-dashboard-modal-large {
            max-width: 800px;
        }

        .admin-dashboard-modal-header {
            padding: 24px 24px 0 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .admin-dashboard-modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .admin-dashboard-modal-close {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .admin-dashboard-modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .admin-dashboard-modal-body {
            padding: 0 24px 24px 24px;
        }

        .admin-dashboard-modal-footer {
            padding: 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .admin-dashboard-info-display {
            padding: 12px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-weight: 500;
            color: #374151;
        }

        /* Stock quantity styling */
        .admin-dashboard-stock-quantity {
            font-weight: 600;
            font-size: 1.1rem;
            color: #1f2937;
        }

        /* Action buttons */
        .admin-dashboard-action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* Notification Styles */
        .admin-dashboard-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            max-width: 400px;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideInRight 0.3s ease-out;
        }

        .admin-dashboard-notification-success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
        }

        .admin-dashboard-notification-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }

        .admin-dashboard-notification-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .admin-dashboard-notification-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .admin-dashboard-notification-close {
            background: none;
            border: none;
            color: currentColor;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .admin-dashboard-notification-close:hover {
            opacity: 1;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Loading Styles */
        .admin-dashboard-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .admin-dashboard-loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .admin-dashboard-error-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .admin-dashboard-action-buttons {
                flex-direction: column;
            }

            .admin-dashboard-modal-content {
                margin: 10px;
                width: calc(100% - 20px);
            }

            .admin-dashboard-modal-header,
            .admin-dashboard-modal-body,
            .admin-dashboard-modal-footer {
                padding-left: 16px;
                padding-right: 16px;
            }

            .admin-dashboard-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }

            /* Form responsive improvements */
            .admin-dashboard-form-row {
                flex-direction: column;
                gap: 16px;
            }

            .admin-dashboard-form-group {
                width: 100%;
            }

            /* Table responsive improvements */
            .admin-dashboard-table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .admin-dashboard-table {
                min-width: 600px;
            }

            /* Button improvements for mobile */
            .admin-dashboard-btn {
                min-height: 44px; /* Minimum touch target */
                padding: 12px 16px;
            }

            .admin-dashboard-btn-sm {
                min-height: 40px;
                padding: 10px 14px;
            }

            /* Stats grid responsive */
            .admin-dashboard-stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .admin-dashboard-stat-card {
                padding: 16px;
            }

            .admin-dashboard-stat-number {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .admin-dashboard-stats-grid {
                grid-template-columns: 1fr;
            }

            .admin-dashboard-modal-content {
                margin: 5px;
                width: calc(100% - 10px);
                max-height: 95vh;
            }

            .admin-dashboard-modal-header,
            .admin-dashboard-modal-body,
            .admin-dashboard-modal-footer {
                padding: 12px;
            }

            .admin-dashboard-table {
                font-size: 0.875rem;
            }

            .admin-dashboard-action-buttons {
                gap: 6px;
            }
        }

        /* Touch improvements */
        @media (hover: none) and (pointer: coarse) {
            .admin-dashboard-btn:hover,
            .admin-dashboard-nav-link:hover {
                background-color: initial;
            }

            .admin-dashboard-btn:active,
            .admin-dashboard-nav-link:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }
        }
    </style>
</head>
<body class="admin-dashboard-container">
    <!-- Sidebar -->
    <aside class="admin-dashboard-sidebar" id="sidebar">
        <!-- Logo -->
        <div class="admin-dashboard-logo">
            <div class="admin-dashboard-logo-icon">IBA</div>
            <div>
                <div class="font-semibold text-lg">Indah Berkah Abadi</div>
                <div class="text-sm text-slate-300">Sistem Inventori</div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="admin-dashboard-nav">
            <!-- Main Navigation -->
            <div class="admin-dashboard-nav-section">
                <div class="admin-dashboard-nav-title">Menu Utama</div>
                <a href="{{ route('admin.dashboard') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>

                {{-- ARCHIVED: Product management moved to supplier dashboard --}}
                {{-- <a href="{{ route('admin.products.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.products*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Kelola Produk
                </a> --}}

                <a href="{{ route('admin.distributions.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.distributions*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    Kelola Distribusi
                </a>

                <a href="{{ route('admin.stores') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.stores') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Kelola Toko
                </a>
            </div>

            <!-- Supplier Management Section -->
            <div class="admin-dashboard-nav-section">
                <div class="admin-dashboard-nav-title">Manajemen Supplier</div>
                {{-- REMOVED: Supplier management moved to supplier dashboard --}}
                {{-- <a href="{{ route('admin.suppliers.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.suppliers*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Kelola Supplier
                </a> --}}

                <a href="{{ route('admin.supplier-deliveries.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.supplier-deliveries*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Penerimaan Barang
                </a>

                <a href="{{ route('admin.returns.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.returns*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                    </svg>
                    Kelola Retur
                </a>
            </div>

            <!-- Settings Section -->
            <div class="admin-dashboard-nav-section">
                <div class="admin-dashboard-nav-title">Pengaturan</div>
                <a href="{{ route('admin.users.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    Kelola Pengguna
                </a>

                <!-- Core Inventory Management -->
                <a href="{{ route('admin.stock.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.stock*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Stock Gudang
                </a>

                <a href="{{ route('admin.store-inventory.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.store-inventory*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Inventori Toko
                </a>

                {{-- SIMPLIFIED: Consolidated inventory views to reduce complexity --}}
                {{-- <a href="{{ route('admin.product-inventory.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.product-inventory*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Inventori Produk
                </a> --}}

                {{-- SIMPLIFIED: Product realization can be accessed through other inventory views --}}
                {{-- <a href="{{ route('admin.product-realization.index') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.product-realization.*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    Realisasi Produk
                </a> --}}

            </div>

            <!-- Download Section -->
            <div class="admin-dashboard-nav-section">
                <div class="admin-dashboard-nav-title">Unduh Data</div>
                <a href="{{ route('admin.download.form') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.download.*') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Unduh Excel
                </a>
            </div>

            <!-- Account Section -->
            <div class="admin-dashboard-nav-section">
                <div class="admin-dashboard-nav-title">Akun</div>
                <a href="{{ route('admin.profile') }}" class="admin-dashboard-nav-link {{ request()->routeIs('admin.profile') ? 'active' : '' }}">
                    <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Profil Saya
                </a>

                <form action="{{ route('logout') }}" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="admin-dashboard-nav-link w-full text-left" style="background: none; border: none; width: 100%;">
                        <svg class="admin-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Keluar
                    </button>
                </form>
            </div>
        </nav>
    </aside>

    <!-- Overlay for mobile -->
    <div class="admin-dashboard-overlay" id="overlay"></div>

    <!-- Main Content -->
    <main class="admin-dashboard-main">
        <!-- Header -->
        <header class="admin-dashboard-header">
            <div class="flex items-center gap-4">
                <button class="admin-dashboard-mobile-toggle" id="mobileToggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <h1 class="text-xl font-semibold text-gray-900">@yield('page-title', 'Dashboard')</h1>
            </div>

            <div class="flex items-center gap-4">
                <!-- User Menu -->
                <div class="admin-dashboard-user-menu">
                    <button class="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100" id="userMenuToggle">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <span class="hidden sm:block text-sm font-medium text-gray-700">{{ auth()->user()->name }}</span>
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div class="admin-dashboard-dropdown" id="userDropdown">
                        <a href="{{ route('admin.profile') }}" class="admin-dashboard-dropdown-item">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Profil Saya
                        </a>
                        <form action="{{ route('logout') }}" method="POST">
                            @csrf
                            <button type="submit" class="admin-dashboard-dropdown-item w-full text-left">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Keluar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-dashboard-content">
            <!-- Flash Messages -->
            @if(session('success'))
                <div class="admin-dashboard-alert admin-dashboard-alert-success mb-6">
                    <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="admin-dashboard-alert admin-dashboard-alert-error mb-6">
                    <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    {{ session('error') }}
                </div>
            @endif

            @if(session('warning'))
                <div class="admin-dashboard-alert admin-dashboard-alert-warning mb-6">
                    <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    {{ session('warning') }}
                </div>
            @endif

            @if(session('info'))
                <div class="admin-dashboard-alert admin-dashboard-alert-info mb-6">
                    <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ session('info') }}
                </div>
            @endif

            @yield('content')
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // Mobile sidebar toggle
        const mobileToggle = document.getElementById('mobileToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        mobileToggle.addEventListener('click', () => {
            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');
        });

        overlay.addEventListener('click', () => {
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
        });

        // User dropdown toggle
        const userMenuToggle = document.getElementById('userMenuToggle');
        const userDropdown = document.getElementById('userDropdown');

        userMenuToggle.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('active');
        });

        document.addEventListener('click', () => {
            userDropdown.classList.remove('active');
        });

        userDropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    </script>

    <!-- Timezone Script -->
    <script>
        // Store user timezone in session storage for JavaScript access
        @if(session('user_timezone'))
            sessionStorage.setItem('user_timezone', '{{ session('user_timezone') }}');
        @endif
    </script>

    <!-- Analytics Sidebar JavaScript -->
    <script src="{{ asset('js/analytics-sidebar.js') }}?v={{ time() }}"></script>

    @stack('scripts')
</body>
</html>