@extends('layouts.admin')

@section('title', 'Detail Retur - Dashboard Admin')
@section('page-title', 'Detail Retur')

@section('content')
<div class="admin-dashboard-returns-container">
    <!-- Header with Back Button -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <div class="admin-dashboard-card-header-content">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.returns.index') }}" 
                       class="admin-dashboard-btn admin-dashboard-btn-secondary admin-dashboard-btn-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        <PERSON><PERSON><PERSON>
                    </a>
                    <div>
                        <h1 class="admin-dashboard-card-title">Detail Retur #{{ $return->id }}</h1>
                        <p class="admin-dashboard-card-subtitle">Informasi lengkap permintaan retur</p>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-card-header-actions">
                @if($return->status === 'requested')
                <div class="flex space-x-2">
                    <button onclick="approveReturn('{{ $return->id }}')"
                            class="admin-dashboard-btn admin-dashboard-btn-success">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Setujui Retur
                    </button>
                    <button onclick="rejectReturn('{{ $return->id }}')"
                            class="admin-dashboard-btn admin-dashboard-btn-danger">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Tolak Retur
                    </button>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Return Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Retur</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Status:</span>
                        <span class="admin-dashboard-status-badge admin-dashboard-status-{{ $return->status }}">
                            {{ $return->status_in_indonesian }}
                        </span>
                    </div>

                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Tanggal Retur:</span>
                        <span class="admin-dashboard-info-value">{{ auth()->user()->formatDate($return->return_date) }}</span>
                    </div>

                    @if($return->approved_date)
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Tanggal Disetujui:</span>
                        <span class="admin-dashboard-info-value">{{ auth()->user()->formatDate($return->approved_date) }}</span>
                    </div>
                    @endif

                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Jumlah:</span>
                        <span class="admin-dashboard-info-value">{{ number_format($return->quantity) }} unit</span>
                    </div>

                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Alasan:</span>
                        <span class="admin-dashboard-info-value">{{ $return->reason_in_indonesian }}</span>
                    </div>

                    @if($return->description)
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Deskripsi:</span>
                        <span class="admin-dashboard-info-value">{{ $return->description }}</span>
                    </div>
                    @endif

                    @if($return->admin_notes)
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Catatan Admin:</span>
                        <span class="admin-dashboard-info-value">{{ $return->admin_notes }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Product Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Produk</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Nama Produk:</span>
                        <span class="admin-dashboard-info-value font-medium">{{ $return->product->name }}</span>
                    </div>

                    @if($return->product->description)
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Deskripsi:</span>
                        <span class="admin-dashboard-info-value">{{ $return->product->description }}</span>
                    </div>
                    @endif

                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Kategori:</span>
                        <span class="admin-dashboard-info-value">{{ $return->product->category ?? 'Tidak ada kategori' }}</span>
                    </div>

                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Supplier:</span>
                        <span class="admin-dashboard-info-value font-medium">{{ $return->supplier->name }}</span>
                    </div>

                    @if($return->supplier->contact_person)
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Kontak Person:</span>
                        <span class="admin-dashboard-info-value">{{ $return->supplier->contact_person }}</span>
                    </div>
                    @endif

                    @if($return->supplier->phone)
                    <div class="admin-dashboard-info-row">
                        <span class="admin-dashboard-info-label">Telepon:</span>
                        <span class="admin-dashboard-info-value">{{ $return->supplier->phone }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Information (if available) -->
    @if($return->supplierDelivery)
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Pengiriman Terkait</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="admin-dashboard-info-row">
                    <span class="admin-dashboard-info-label">Tanggal Pengiriman:</span>
                    <span class="admin-dashboard-info-value">{{ auth()->user()->formatDate($return->supplierDelivery->delivery_date) }}</span>
                </div>

                <div class="admin-dashboard-info-row">
                    <span class="admin-dashboard-info-label">Jumlah Dikirim:</span>
                    <span class="admin-dashboard-info-value">{{ number_format($return->supplierDelivery->quantity) }} unit</span>
                </div>

                <div class="admin-dashboard-info-row">
                    <span class="admin-dashboard-info-label">Jumlah Diterima:</span>
                    <span class="admin-dashboard-info-value">{{ number_format($return->supplierDelivery->received_quantity ?? 0) }} unit</span>
                </div>

                @if($return->supplierDelivery->received_date)
                <div class="admin-dashboard-info-row">
                    <span class="admin-dashboard-info-label">Tanggal Diterima:</span>
                    <span class="admin-dashboard-info-value">{{ auth()->user()->formatDate($return->supplierDelivery->received_date) }}</span>
                </div>
                @endif

                <div class="admin-dashboard-info-row">
                    <span class="admin-dashboard-info-label">Status Pengiriman:</span>
                    <span class="admin-dashboard-status-badge admin-dashboard-status-{{ $return->supplierDelivery->status }}">
                        @if($return->supplierDelivery->status === 'pending') Pending
                        @elseif($return->supplierDelivery->status === 'received') Diterima
                        @elseif($return->supplierDelivery->status === 'partial') Sebagian
                        @elseif($return->supplierDelivery->status === 'cancelled') Dibatalkan
                        @else {{ ucfirst($return->supplierDelivery->status) }}
                        @endif
                    </span>
                </div>

                @if($return->supplierDelivery->notes)
                <div class="admin-dashboard-info-row md:col-span-3">
                    <span class="admin-dashboard-info-label">Catatan Pengiriman:</span>
                    <span class="admin-dashboard-info-value">{{ $return->supplierDelivery->notes }}</span>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Timeline -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Timeline Retur</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="admin-dashboard-timeline">
                <div class="admin-dashboard-timeline-item admin-dashboard-timeline-item-completed">
                    <div class="admin-dashboard-timeline-marker"></div>
                    <div class="admin-dashboard-timeline-content">
                        <h4 class="admin-dashboard-timeline-title">Retur Dibuat</h4>
                        <p class="admin-dashboard-timeline-description">Permintaan retur dibuat oleh gudang</p>
                        <p class="admin-dashboard-timeline-date">{{ auth()->user()->formatDate($return->return_date) }}</p>
                    </div>
                </div>

                @if($return->status !== 'requested')
                <div class="admin-dashboard-timeline-item {{ $return->status === 'rejected' ? 'admin-dashboard-timeline-item-rejected' : 'admin-dashboard-timeline-item-completed' }}">
                    <div class="admin-dashboard-timeline-marker"></div>
                    <div class="admin-dashboard-timeline-content">
                        <h4 class="admin-dashboard-timeline-title">
                            {{ $return->status === 'rejected' ? 'Retur Ditolak' : 'Retur Disetujui' }}
                        </h4>
                        <p class="admin-dashboard-timeline-description">
                            {{ $return->status === 'rejected' ? 'Permintaan retur ditolak oleh admin gudang' : 'Permintaan retur disetujui oleh admin gudang' }}
                        </p>
                        @if($return->approved_date)
                        <p class="admin-dashboard-timeline-date">{{ auth()->user()->formatDate($return->approved_date) }}</p>
                        @endif
                    </div>
                </div>
                @endif

                @if(in_array($return->status, ['approved', 'in_transit', 'completed']))
                <div class="admin-dashboard-timeline-item {{ in_array($return->status, ['in_transit', 'completed']) ? 'admin-dashboard-timeline-item-completed' : 'admin-dashboard-timeline-item-pending' }}">
                    <div class="admin-dashboard-timeline-marker"></div>
                    <div class="admin-dashboard-timeline-content">
                        <h4 class="admin-dashboard-timeline-title">Dalam Perjalanan</h4>
                        <p class="admin-dashboard-timeline-description">Produk sedang dalam perjalanan kembali ke supplier</p>
                    </div>
                </div>

                <div class="admin-dashboard-timeline-item {{ $return->status === 'completed' ? 'admin-dashboard-timeline-item-completed' : 'admin-dashboard-timeline-item-pending' }}">
                    <div class="admin-dashboard-timeline-marker"></div>
                    <div class="admin-dashboard-timeline-content">
                        <h4 class="admin-dashboard-timeline-title">Retur Selesai</h4>
                        <p class="admin-dashboard-timeline-description">Produk telah diterima kembali oleh supplier</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div id="approvalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form id="approvalForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Setujui Retur</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Catatan Admin (Opsional)</label>
                        <textarea name="admin_notes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                    </div>
                </div>
                <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">Batal</button>
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-success">Setujui Retur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form id="rejectionForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tolak Retur</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Alasan Penolakan <span class="text-red-500">*</span></label>
                        <textarea name="admin_notes" rows="3" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500" placeholder="Jelaskan alasan penolakan retur..."></textarea>
                    </div>
                </div>
                <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">Batal</button>
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">Tolak Retur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveReturn(returnId) {
    document.getElementById('approvalForm').action = `/admin/returns/${returnId}/approve`;
    document.getElementById('approvalModal').classList.remove('hidden');
}

function rejectReturn(returnId) {
    document.getElementById('rejectionForm').action = `/admin/returns/${returnId}/reject`;
    document.getElementById('rejectionModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('approvalModal').classList.add('hidden');
    document.getElementById('rejectionModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('approvalModal').addEventListener('click', function(e) {
    if (e.target === this) closeModal();
});

document.getElementById('rejectionModal').addEventListener('click', function(e) {
    if (e.target === this) closeModal();
});
</script>
@endsection
