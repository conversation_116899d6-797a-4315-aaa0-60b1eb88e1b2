@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON> - Dashboard Admin')
@section('page-title', 'Kelola Retur')

@section('content')
<div class="admin-dashboard-returns-container">
    <!-- Header with Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <div class="admin-dashboard-card-header-content">
                <h1 class="admin-dashboard-card-title">Ke<PERSON><PERSON> Re<PERSON></h1>
                <p class="admin-dashboard-card-subtitle">Kelola permintaan retur dari gudang ke supplier</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="admin-dashboard-stats-grid">
        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-blue">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-label">Total Retur</p>
                <p class="admin-dashboard-stat-value">{{ number_format($stats['total']) }}</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-yellow">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-label">Menunggu Persetujuan</p>
                <p class="admin-dashboard-stat-value">{{ number_format($stats['requested']) }}</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-blue">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-label">Disetujui</p>
                <p class="admin-dashboard-stat-value">{{ number_format($stats['approved']) }}</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-green">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-label">Selesai</p>
                <p class="admin-dashboard-stat-value">{{ number_format($stats['completed']) }}</p>
            </div>
        </div>
    </div>

    <!-- Time Period Filter -->
    @include('admin.components.time-period-filter')

    <!-- Filters and Search -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" class="admin-dashboard-filter-form">
                <!-- Preserve time period -->
                <input type="hidden" name="period" value="{{ request('period', 'month') }}">

                <!-- Search -->
                <div class="admin-dashboard-form-group">
                    <label class="admin-dashboard-form-label">Cari</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Cari nama produk atau supplier..."
                           class="admin-dashboard-form-input">
                </div>

                <!-- Status Filter -->
                <div class="admin-dashboard-form-group">
                    <label class="admin-dashboard-form-label">Status</label>
                    <select name="status" class="admin-dashboard-form-select">
                        <option value="">Semua Status</option>
                        <option value="requested" {{ request('status') === 'requested' ? 'selected' : '' }}>Diminta</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                        <option value="in_transit" {{ request('status') === 'in_transit' ? 'selected' : '' }}>Dalam Perjalanan</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                    </select>
                </div>

                <!-- Reason Filter -->
                <div class="admin-dashboard-form-group">
                    <label class="admin-dashboard-form-label">Alasan</label>
                    <select name="reason" class="admin-dashboard-form-select">
                        <option value="">Semua Alasan</option>
                        <option value="damaged" {{ request('reason') === 'damaged' ? 'selected' : '' }}>Rusak</option>
                        <option value="expired" {{ request('reason') === 'expired' ? 'selected' : '' }}>Kadaluarsa</option>
                        <option value="defective" {{ request('reason') === 'defective' ? 'selected' : '' }}>Cacat</option>
                        <option value="overstock" {{ request('reason') === 'overstock' ? 'selected' : '' }}>Kelebihan Stok</option>
                        <option value="shortage" {{ request('reason') === 'shortage' ? 'selected' : '' }}>Kekurangan</option>
                        <option value="other" {{ request('reason') === 'other' ? 'selected' : '' }}>Lainnya</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="admin-dashboard-form-group admin-dashboard-form-group-button">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary admin-dashboard-btn-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Retur Gudang ke Supplier</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Supplier</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                @if($return->supplierDelivery)
                                <div class="text-sm text-gray-500">Pengiriman: {{ $return->supplierDelivery->delivery_date->format('d/m/Y') }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->supplier->name }}</div>
                                @if($return->supplier->contact_person)
                                <div class="text-sm text-gray-500">{{ $return->supplier->contact_person }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($return->quantity) }}</div>
                                <div class="text-xs text-gray-500">unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">{{ $return->reason_in_indonesian }}</div>
                                @if($return->description)
                                <div class="text-xs text-gray-500 mt-1">{{ Str::limit($return->description, 50) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ auth()->user()->formatDate($return->return_date) }}</div>
                                @if($return->approved_date)
                                <div class="text-xs text-gray-500">Disetujui: {{ auth()->user()->formatDate($return->approved_date) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="admin-dashboard-status-badge admin-dashboard-status-{{ $return->status }}">
                                    {{ $return->status_in_indonesian }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.returns.show', $return) }}"
                                       class="admin-dashboard-btn admin-dashboard-btn-secondary admin-dashboard-btn-sm">
                                        Lihat
                                    </a>
                                    @if($return->status === 'requested')
                                    <button onclick="approveReturn('{{ $return->id }}')"
                                            class="admin-dashboard-btn admin-dashboard-btn-success admin-dashboard-btn-sm">
                                        Setujui
                                    </button>
                                    <button onclick="rejectReturn('{{ $return->id }}')"
                                            class="admin-dashboard-btn admin-dashboard-btn-danger admin-dashboard-btn-sm">
                                        Tolak
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="admin-dashboard-empty-state">
                                    <svg class="admin-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="admin-dashboard-empty-title">Belum ada retur</p>
                                    <p class="admin-dashboard-empty-description">Retur akan muncul ketika ada permintaan retur dari gudang ke supplier</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($returns->hasPages())
            <div class="admin-dashboard-pagination">
                {{ $returns->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
<!-- Approval Modal -->
<div id="approvalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form id="approvalForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Setujui Retur</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Catatan Admin (Opsional)</label>
                        <textarea name="admin_notes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                    </div>
                </div>
                <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">Batal</button>
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-success">Setujui Retur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form id="rejectionForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tolak Retur</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Alasan Penolakan <span class="text-red-500">*</span></label>
                        <textarea name="admin_notes" rows="3" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500" placeholder="Jelaskan alasan penolakan retur..."></textarea>
                    </div>
                </div>
                <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">Batal</button>
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">Tolak Retur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveReturn(returnId) {
    document.getElementById('approvalForm').action = `/admin/returns/${returnId}/approve`;
    document.getElementById('approvalModal').classList.remove('hidden');
}

function rejectReturn(returnId) {
    document.getElementById('rejectionForm').action = `/admin/returns/${returnId}/reject`;
    document.getElementById('rejectionModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('approvalModal').classList.add('hidden');
    document.getElementById('rejectionModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('approvalModal').addEventListener('click', function(e) {
    if (e.target === this) closeModal();
});

document.getElementById('rejectionModal').addEventListener('click', function(e) {
    if (e.target === this) closeModal();
});
</script>
@endsection
