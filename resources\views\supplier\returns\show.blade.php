@extends('layouts.supplier')

@section('title', 'Detail Retur - Dashboard Supplier')
@section('page-title', 'Detail Retur')

@section('content')
<div class="space-y-6">
    <!-- Header with Back Button -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center space-x-3 mb-2">
                        <a href="{{ route('supplier.returns.index') }}" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>
                        <h1 class="text-2xl font-bold text-gray-900">Detail Retur</h1>
                    </div>
                    <p class="text-gray-600">Informasi lengkap permintaan retur</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    @if($return->status === 'requested')
                    <button onclick="approveReturn('{{ $return->id }}')" class="supplier-dashboard-btn supplier-dashboard-btn-success">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Setujui Retur
                    </button>
                    <button onclick="rejectReturn('{{ $return->id }}')" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Tolak Retur
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Return Status Card -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-1">Status Retur</h3>
                    <span class="supplier-dashboard-status-badge supplier-dashboard-status-{{ $return->status }} text-base">
                        @if($return->status === 'requested') Diminta
                        @elseif($return->status === 'approved') Disetujui
                        @elseif($return->status === 'rejected') Ditolak
                        @else {{ ucfirst($return->status) }}
                        @endif
                    </span>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Tanggal Retur</p>
                    <p class="text-lg font-medium text-gray-900">{{ auth()->user()->formatDate($return->return_date) }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Information -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h2 class="supplier-dashboard-card-title">Informasi Produk</h2>
            </div>
            <div class="supplier-dashboard-card-content">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Produk</label>
                        <p class="text-lg font-medium text-gray-900">{{ $return->product->name }}</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Jumlah Retur</label>
                            <p class="text-lg font-medium text-blue-600">{{ number_format($return->quantity) }} unit</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Alasan Retur</label>
                            <p class="text-sm text-gray-900">
                                @if($return->reason === 'damaged') Produk Rusak
                                @elseif($return->reason === 'defective') Produk Cacat
                                @elseif($return->reason === 'wrong_item') Barang Salah
                                @elseif($return->reason === 'excess_quantity') Kelebihan Jumlah
                                @elseif($return->reason === 'quality_issue') Masalah Kualitas
                                @elseif($return->reason === 'other') Lainnya
                                @else {{ ucfirst($return->reason) }}
                                @endif
                            </p>
                        </div>
                    </div>

                    @if($return->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
                        <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{{ $return->description }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Delivery Context -->
        @if($return->supplierDelivery)
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h2 class="supplier-dashboard-card-title">Konteks Pengiriman</h2>
            </div>
            <div class="supplier-dashboard-card-content">
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Jumlah Dikirim</label>
                            <p class="text-lg font-medium text-gray-900">{{ number_format($return->supplierDelivery->quantity) }} unit</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Jumlah Diterima</label>
                            <p class="text-lg font-medium text-gray-900">{{ number_format($return->supplierDelivery->received_quantity ?? 0) }} unit</p>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Pengiriman</label>
                        <p class="text-sm text-gray-900">{{ auth()->user()->formatDate($return->supplierDelivery->delivery_date) }}</p>
                    </div>

                    @if($return->supplierDelivery->notes)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Catatan Pengiriman</label>
                        <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{{ $return->supplierDelivery->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Admin Response -->
    @if($return->admin_notes || $return->approved_date)
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Respons Supplier</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="space-y-4">
                @if($return->approved_date)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Respons</label>
                    <p class="text-sm text-gray-900">{{ auth()->user()->formatDate($return->approved_date) }}</p>
                </div>
                @endif

                @if($return->admin_notes)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        @if($return->status === 'approved') Catatan Persetujuan
                        @elseif($return->status === 'rejected') Alasan Penolakan
                        @else Catatan
                        @endif
                    </label>
                    <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                        <p class="text-sm text-gray-900">{{ $return->admin_notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Timeline -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Timeline Retur</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="flow-root">
                <ul class="-mb-8">
                    <!-- Return Requested -->
                    <li>
                        <div class="relative pb-8">
                            <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            Retur diminta pada <time>{{ auth()->user()->formatDate($return->return_date) }}</time>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- Return Response -->
                    @if($return->approved_date)
                    <li>
                        <div class="relative">
                            <div class="relative flex space-x-3">
                                <div>
                                    @if($return->status === 'approved')
                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </span>
                                    @else
                                    <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </span>
                                    @endif
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            Retur {{ $return->status === 'approved' ? 'disetujui' : 'ditolak' }} pada 
                                            <time>{{ auth()->user()->formatDate($return->approved_date) }}</time>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    @endif
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Approve Return Modal -->
<div id="approveModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Setujui Retur</h3>
            <button type="button" onclick="closeApproveModal()" class="supplier-dashboard-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveForm" method="POST">
            @csrf
            @method('PATCH')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Setujui Permintaan Retur</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyetujui permintaan retur ini?
                        </p>
                        <div>
                            <label for="approveNotes" class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan (Opsional)
                            </label>
                            <textarea name="admin_notes"
                                      id="approveNotes"
                                      rows="3"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeApproveModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Ya, Setujui
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reject Return Modal -->
<div id="rejectModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Tolak Retur</h3>
            <button type="button" onclick="closeRejectModal()" class="supplier-dashboard-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="rejectForm" method="POST">
            @csrf
            @method('PATCH')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Tolak Permintaan Retur</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Berikan alasan penolakan untuk permintaan retur ini.
                        </p>
                        <div>
                            <label for="rejectNotes" class="block text-sm font-medium text-gray-700 mb-2">
                                Alasan Penolakan <span class="text-red-500">*</span>
                            </label>
                            <textarea name="admin_notes"
                                      id="rejectNotes"
                                      rows="4"
                                      required
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
                                      placeholder="Jelaskan alasan penolakan retur..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeRejectModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Ya, Tolak
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function approveReturn(returnId) {
    const modal = document.getElementById('approveModal');
    const form = document.getElementById('approveForm');

    form.action = `/supplier/returns/${returnId}/approve`;
    modal.classList.add('active');
}

function closeApproveModal() {
    const modal = document.getElementById('approveModal');
    modal.classList.remove('active');

    // Reset form
    document.getElementById('approveForm').reset();
}

function rejectReturn(returnId) {
    const modal = document.getElementById('rejectModal');
    const form = document.getElementById('rejectForm');

    form.action = `/supplier/returns/${returnId}/reject`;
    modal.classList.add('active');
}

function closeRejectModal() {
    const modal = document.getElementById('rejectModal');
    modal.classList.remove('active');

    // Reset form
    document.getElementById('rejectForm').reset();
}

// Close modals when clicking outside
document.getElementById('approveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApproveModal();
    }
});

document.getElementById('rejectModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRejectModal();
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeApproveModal();
        closeRejectModal();
    }
});
</script>
@endpush

@endsection
